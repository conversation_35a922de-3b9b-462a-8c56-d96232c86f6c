const express = require('express');
const cors = require('cors');
const fs = require('fs').promises;
const fsSync = require('fs');
const path = require('path');
const http = require('http');
const WebSocket = require('ws');
const axios = require('axios');
const { spawn } = require('child_process');
const os = require('os');
const { batchGenerateVideosWithOrchestrator, stopVideoOrchestrator } = require('./batch_videos.js');
const { generateSafeVideoFilename, downloadVideo } = require('./utils.js');

const app = express();
const PORT = process.env.PORT || 3001;

// 获取服务器URL - 支持局域网访问
function getServerUrl() {
    // 优先使用环境变量中的服务器地址
    if (process.env.SERVER_URL) {
        return process.env.SERVER_URL;
    }

    // 获取本机IP地址
    const networkInterfaces = os.networkInterfaces();
    let localIP = 'localhost';

    // 查找第一个非内部的IPv4地址
    for (const interfaceName in networkInterfaces) {
        const interfaces = networkInterfaces[interfaceName];
        for (const iface of interfaces) {
            if (iface.family === 'IPv4' && !iface.internal) {
                localIP = iface.address;
                break;
            }
        }
        if (localIP !== 'localhost') break;
    }

    return `http://${localIP}:${PORT}`;
}

app.use(cors());
app.use(express.json({ limit: '50mb' }));

// --- HTTP and WebSocket Server Setup ---
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

// --- 图片URL映射存储 ---
// 格式: { localUrl: remoteUrl }
const imageUrlMapping = new Map();

// 同步加载配置的函数
function loadAppConfigSync() {
    try {
        const configPath = path.join(__dirname, 'config_data', 'app_config.json');
        if (fsSync.existsSync(configPath)) {
            const configData = fsSync.readFileSync(configPath, 'utf8');
            return JSON.parse(configData);
        }
    } catch (error) {
        console.warn(`⚠️ 同步加载配置失败: ${error.message}`);
    }
    return null;
}

// 工具函数：将本地图片转换为Base64格式
function convertLocalImageToBase64(localImagePath) {
    try {
        if (!fsSync.existsSync(localImagePath)) {
            console.warn(`⚠️ 本地图片文件不存在: ${localImagePath}`);
            return null;
        }

        const imageData = fsSync.readFileSync(localImagePath);
        const base64Data = imageData.toString('base64');
        const mimeType = localImagePath.toLowerCase().endsWith('.png') ? 'image/png' : 'image/jpeg';
        const dataUrl = `data:${mimeType};base64,${base64Data}`;

        console.log(`🔄 本地图片转换为Base64: ${localImagePath} -> data:${mimeType};base64,[${base64Data.length}字符]`);
        return dataUrl;
    } catch (error) {
        console.error(`❌ 转换本地图片为Base64失败: ${error.message}`);
        return null;
    }
}

// 工具函数：获取图片的实际URL（用于视频生成）
function getActualImageUrl(imageInput) {
    if (typeof imageInput === 'string' && imageInput.startsWith('/generated_images/')) {
        // 首先尝试URL映射（兼容旧的远程图片）
        const remoteUrl = imageUrlMapping.get(imageInput);
        if (remoteUrl) {
            console.log(`🔗 URL映射查找: ${imageInput} -> ${remoteUrl}`);
            return remoteUrl;
        }

        // 如果没有映射，构建可访问的图片URL
        const filename = imageInput.replace('/generated_images/', '');

        // 首先检查网络路径（主要存储位置）
        const config = loadAppConfigSync();
        if (config && config.imageSavePath) {
            const networkImagePath = path.join(config.imageSavePath, filename);
            if (fsSync.existsSync(networkImagePath)) {
                console.log(`🌐 找到网络图片文件: ${networkImagePath}`);
                // 构建可访问的URL - 支持局域网访问
                const serverUrl = getServerUrl();
                const imageUrl = `${serverUrl}/api/network-file/${encodeURIComponent(filename)}`;
                console.log(`🔗 构建图片访问URL: ${imageUrl}`);
                return imageUrl;
            }
        }

        // 如果网络路径不存在，检查本地备用路径
        const localImagePath = path.join(__dirname, 'image', 'generated_images', filename);
        if (fsSync.existsSync(localImagePath)) {
            console.log(`📁 找到本地图片文件: ${localImagePath}`);
            // 构建可访问的URL - 支持局域网访问
            const serverUrl = getServerUrl();
            const imageUrl = `${serverUrl}/generated_images/${encodeURIComponent(filename)}`;
            console.log(`🔗 构建本地图片访问URL: ${imageUrl}`);
            return imageUrl;
        }

        console.warn(`⚠️ 未找到图片文件: ${imageInput}`);
        return null;
    }
    return imageInput; // 已经是远程URL或其他格式
}

// 工具函数：将远程URL转换为本地URL（如果本地文件存在）
function convertRemoteUrlsToLocal(row) {
    // 同步读取配置文件
    let imageSavePath = path.join(__dirname, 'image', 'generated_images');
    try {
        const configPath = path.join(__dirname, 'config_data', 'app_config.json');
        if (fsSync.existsSync(configPath)) {
            const configData = JSON.parse(fsSync.readFileSync(configPath, 'utf8'));
            if (configData.imageSavePath && configData.imageSavePath.trim()) {
                imageSavePath = configData.imageSavePath.trim();
            }
        }
    } catch (error) {
        console.warn(`⚠️ 读取配置文件失败，使用默认路径: ${error.message}`);
    }

    // 处理 generatedImages 数组
    if (row.generatedImages && Array.isArray(row.generatedImages)) {
        row.generatedImages = row.generatedImages.map(url => {
            if (typeof url === 'string' && url.startsWith('http')) {
                // 尝试根据URL生成可能的本地文件名
                const localUrl = findLocalImageForRemoteUrl(url, imageSavePath);
                if (localUrl) {
                    console.log(`🔄 转换远程URL为本地URL: ${url} -> ${localUrl}`);
                    // 建立映射关系
                    imageUrlMapping.set(localUrl, url);
                    return localUrl;
                }
            }
            return url;
        });
    }

    // 处理 selectedRefImage
    if (row.selectedRefImage && typeof row.selectedRefImage === 'string' && row.selectedRefImage.startsWith('http')) {
        const localUrl = findLocalImageForRemoteUrl(row.selectedRefImage, imageSavePath);
        if (localUrl) {
            console.log(`🔄 转换选中图片远程URL为本地URL: ${row.selectedRefImage} -> ${localUrl}`);
            // 建立映射关系
            imageUrlMapping.set(localUrl, row.selectedRefImage);
            row.selectedRefImage = localUrl;
        }
    }

    return row;
}

// 工具函数：根据远程URL查找对应的本地图片文件
function findLocalImageForRemoteUrl(remoteUrl, imageSavePath) {
    try {
        // 获取所有本地图片文件
        if (!fsSync.existsSync(imageSavePath)) {
            return null;
        }

        const files = fsSync.readdirSync(imageSavePath);
        const imageFiles = files.filter(file =>
            file.toLowerCase().endsWith('.jpg') ||
            file.toLowerCase().endsWith('.jpeg') ||
            file.toLowerCase().endsWith('.png')
        );

        // 简单策略：按时间戳匹配（这里可以根据实际情况优化匹配算法）
        // 由于远程URL中包含时间戳信息，我们可以尝试匹配
        for (const file of imageFiles) {
            // 检查文件是否存在
            const fullPath = path.join(imageSavePath, file);
            if (fsSync.existsSync(fullPath)) {
                // 返回本地URL格式
                return `/generated_images/${file}`;
            }
        }

        return null;
    } catch (error) {
        console.warn(`⚠️ 查找本地图片文件失败: ${error.message}`);
        return null;
    }
}

// 工具函数：向所有连接的WebSocket客户端广播消息
function broadcastToWebSocketClients(message) {
    wss.clients.forEach((client) => {
        if (client.readyState === WebSocket.OPEN) {
            try {
                client.send(JSON.stringify(message));
            } catch (error) {
                console.error('广播WebSocket消息失败:', error);
            }
        }
    });
}

// --- WebSocket Logic for Batch Processing ---

// 图片任务队列 (新增)
const imageTaskQueue = [];
let activeImageTasks = 0; // 当前活动的图片生成任务数
// 图片生成并发数设置为3
const IMAGE_BATCH_CONCURRENCY = 3;

// 重构为支持并发的处理器（带1秒间隔）
async function processImageBatchQueue() {
    console.log(`[Image Queue] 检查队列状态: 活动任务 ${activeImageTasks}/${IMAGE_BATCH_CONCURRENCY}, 队列剩余 ${imageTaskQueue.length}`);

    // 检查是否可以启动新任务
    if (activeImageTasks < IMAGE_BATCH_CONCURRENCY && imageTaskQueue.length > 0) {
        const task = imageTaskQueue.shift();
        const { ws, rowId, prompt, ratio, model, strength, negativePrompt } = task;

        console.log(`[Image Queue][${rowId}] 启动新任务 (${activeImageTasks + 1}/${IMAGE_BATCH_CONCURRENCY})...`);

        // 增加活动任务计数
        activeImageTasks++;

        // 异步处理任务，不等待完成
        processImageTask(task).catch(error => {
            console.error(`[Image Queue][${rowId}] 任务处理出错:`, error);
        }).finally(() => {
            // 任务完成后减少计数并检查是否有更多任务
            activeImageTasks--;
            console.log(`[Image Queue][${rowId}] 任务完成，活动任务数: ${activeImageTasks}/${IMAGE_BATCH_CONCURRENCY}`);

            // 如果还有队列任务，1秒后继续处理
            if (imageTaskQueue.length > 0) {
                setTimeout(() => {
                    processImageBatchQueue();
                }, 1000); // 1秒间隔
            } else if (activeImageTasks === 0) {
                console.log('[Image Queue] 所有图片任务处理完毕。');
            }
        });

        // 如果还有队列任务且未达到并发上限，1秒后启动下一个任务
        if (imageTaskQueue.length > 0 && activeImageTasks < IMAGE_BATCH_CONCURRENCY) {
            setTimeout(() => {
                processImageBatchQueue();
            }, 1000); // 1秒间隔
        }
    }

    if (activeImageTasks >= IMAGE_BATCH_CONCURRENCY) {
        console.log(`[Image Queue] 已达到并发上限 (${activeImageTasks}/${IMAGE_BATCH_CONCURRENCY})，等待任务完成...`);
    }
    if (imageTaskQueue.length === 0 && activeImageTasks === 0) {
        console.log('[Image Queue] 队列为空且无活动任务。');
    }
}

// 处理单个图片任务
async function processImageTask(task) {
    const { ws, rowId, prompt, ratio, model, strength, negativePrompt } = task;

    try {
        // 使用 calculateDimensions 计算尺寸
        const { width, height } = calculateDimensions(ratio);
        console.log(`[Image Queue][${rowId}] 图片尺寸: ${width}x${height}`);

        // 处理图片生成任务
        await callImageScript(prompt, negativePrompt, model, strength, width, height, ws, rowId);

        console.log(`[Image Queue][${rowId}] 任务处理完毕。`);

    } catch (error) {
        console.error(`[Image Queue][${rowId}] 任务失败:`, error);
        // 确保即使失败也通知前端
        if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
                type: 'image-status-update',
                data: { rowId: rowId, status: 'error', message: error.message }
            }));
        }
    }
}

wss.on('connection', (ws) => {
    console.log('WebSocket 客户端已连接');

    ws.on('message', (message) => {
        try {
            const parsedMessage = JSON.parse(message);
            console.log('收到 WebSocket 消息:', parsedMessage.type);

            if (parsedMessage.type === 'batch-generate-videos') {
                console.log('🎬 收到批量视频生成请求');
                // 增加健壮性：同时处理 data 是数组或 data.rows 是数组的情况
                let tasks;

                if (parsedMessage.data && Array.isArray(parsedMessage.data.rows)) {
                    tasks = parsedMessage.data.rows;
                } else if (Array.isArray(parsedMessage.data)) {
                    tasks = parsedMessage.data;
                }

                if (!tasks) {
                    console.error('❌ 批量任务数据格式错误');
                    return;
                }

                console.log(`📊 收到 ${tasks.length} 个批量视频生成任务`);

                // 调用新的编排器模块来处理任务，传递URL映射查找函数
                batchGenerateVideosWithOrchestrator(tasks, ws, (localUrl) => {
                    return imageUrlMapping.get(localUrl);
                });
            } else if (parsedMessage.type === 'generate-image') {
                const { rowId, prompt, ratio, model, strength, negativePrompt } = parsedMessage.data;
                console.log(`[Image Queue] 收到图片生成任务，入队 - rowId: ${rowId}, prompt: ${prompt}`);
                
                // 立即发送开始状态，告知用户任务已入队
                ws.send(JSON.stringify({ 
                    type: 'image-status-update', 
                    data: { rowId: rowId, status: 'processing', images: [] } 
                }));

                // 将任务加入队列
                imageTaskQueue.push({ ws, rowId, prompt, ratio, model, strength, negativePrompt });

                // 启动队列处理器
                processImageBatchQueue();
            }
        } catch (error) {
            console.error('处理 WebSocket 消息失败:', error);
            ws.send(JSON.stringify({ type: 'error', message: '无效的JSON消息' }));
        }
    });

    ws.on('close', () => {
        console.log('WebSocket 客户端已断开');
    });
});

// --- Helpers and Config ---
const ensureDirectories = async () => {
    const dirs = [
        'config_data',
        'tasks',
        'image/generated_images',
        'image/generated_videos',
        'videos' // 确保python脚本的默认输出目录存在
    ];
    
    for (const dir of dirs) {
        const dirPath = path.join(__dirname, dir);
        if (!fsSync.existsSync(dirPath)) {
            await fs.mkdir(dirPath, { recursive: true });
            console.log(`创建目录: ${dirPath}`);
        }
    }
};
ensureDirectories();

const CONFIG_FILE_PATH = path.join(__dirname, 'config_data', 'app_config.json');

async function loadAppConfig() {
    try {
        if (fsSync.existsSync(CONFIG_FILE_PATH)) {
            const data = await fs.readFile(CONFIG_FILE_PATH, 'utf-8');
            const config = JSON.parse(data);
            
            // 兼容性迁移：将旧的 imageSessions 迁移到新的 imageApiKey
            if (config.imageSessions && !config.imageApiKey) {
                config.imageApiKey = config.imageSessions;
                console.log('🔄 配置迁移：imageSessions -> imageApiKey');
            }
            
            // 设置默认值
            if (!config.videoRequireAuth) config.videoRequireAuth = false;
            if (!config.videoApiKey) config.videoApiKey = '';
            
            return config;
        }
    } catch (error) {
        console.error("加载应用配置出错:", error);
    }
    // 返回新的配置结构
    return { 
        imageApiUrl: 'http://127.0.0.1:8000',
        imageApiKey: '',
        videoApiUrl: 'http://localhost:8002',
        videoRequireAuth: false,
        videoApiKey: '',
        videoSavePath: '', 
        imageSavePath: '' 
    };
}

async function saveAppConfig(config) {
    try {
        await fs.writeFile(CONFIG_FILE_PATH, JSON.stringify(config, null, 2), 'utf-8');
        console.log('应用配置已保存');
    } catch (error) {
        console.error("保存应用配置出错:", error);
    }
}

function normalizePathForExpress(inputPath) {
    try {
        if (typeof inputPath !== 'string') return inputPath;
        if (inputPath.startsWith('\\\\')) {
            if (fsSync.existsSync(inputPath)) return inputPath;
                return null;
        }
        return path.resolve(inputPath);
    } catch (error) {
        console.warn(`⚠️  路径规范化失败: ${inputPath}, 错误: ${error.message}`);
        return null;
    }
}

function isAbsolutePath(filePath) {
    if (filePath.startsWith('\\\\')) return true;
    if (/^[a-zA-Z]:[\\\/]/.test(filePath)) return true;
    if (filePath.startsWith('/')) return true;
    return false;
}

function resolveImagePath(frontendPath) {
    try {
        const configPath = path.join(__dirname, 'config_data', 'app_config.json');
        let customImagePath = null;
        if (fsSync.existsSync(configPath)) {
            const configData = JSON.parse(fsSync.readFileSync(configPath, 'utf8'));
            if (configData.imageSavePath && configData.imageSavePath.trim()) {
                customImagePath = configData.imageSavePath.trim();
            }
        }
        let fileName = frontendPath;
        if (isAbsolutePath(frontendPath)) {
            fileName = path.basename(frontendPath);
        } else {
            if (fileName.startsWith('image/generated_images/')) fileName = fileName.replace('image/generated_images/', '');
            else if (fileName.startsWith('generated_images/')) fileName = fileName.replace('generated_images/', '');
            else if (fileName.includes('/')) fileName = path.basename(fileName);
        }
        try {
            if (fileName.includes('%')) fileName = decodeURIComponent(fileName);
        } catch (error) {
            console.warn(`📁 ⚠️  文件名URL解码失败，使用原始文件名: ${error.message}`);
        }
        const possiblePaths = [];
        if (customImagePath) possiblePaths.push(path.join(customImagePath, fileName));
        possiblePaths.push(path.join(__dirname, 'image', 'generated_images', fileName));
        if (isAbsolutePath(frontendPath)) possiblePaths.push(frontendPath);
        for (const testPath of possiblePaths) {
            if (fsSync.existsSync(testPath)) return testPath;
        }
        return customImagePath ? path.join(customImagePath, fileName) : path.join(__dirname, 'image', 'generated_images', fileName);
    } catch (error) {
        console.warn(`⚠️  路径解析失败: ${error.message}, 使用原始路径: ${frontendPath}`);
        return frontendPath;
    }
}

// --- Static File Serving ---
(async () => {
    const config = await loadAppConfig();
    const imageSavePath = config.imageSavePath || path.join(__dirname, 'image', 'generated_images');
    const videoSavePath = config.videoSavePath || path.join(__dirname, 'videos');
    
    // 检查是否为网络路径，并据此配置静态服务
    if (isNetworkPath(imageSavePath)) {
        console.log(`🌐 检测到网络图片路径: ${imageSavePath}`);
        console.log(`⚠️  网络路径将通过 /api/network-file 端点提供访问`);
        
        // 为网络路径添加特殊的中间件，重定向到网络文件端点
        app.use('/generated_images', (req, res, next) => {
            const requestedFile = path.join(imageSavePath, req.path);
            console.log(`📂 网络图片资源请求: ${req.path} -> ${requestedFile}`);
            console.log(`📋 文件是否存在: ${fsSync.existsSync(requestedFile)}`);
            
            // 重定向到网络文件端点
            const filename = req.path.startsWith('/') ? req.path.substring(1) : req.path;
            const redirectUrl = `/api/network-file/${encodeURIComponent(filename)}`;
            console.log(`🔄 重定向到网络文件端点: ${redirectUrl}`);
            res.redirect(redirectUrl);
        });
    } else {
        console.log(`📁 使用本地图片路径: ${imageSavePath}`);
        
        // 添加静态资源访问日志中间件
        app.use('/generated_images', (req, res, next) => {
            const requestedFile = path.join(imageSavePath, req.path);
            console.log(`📂 图片资源请求: ${req.path} -> ${requestedFile}`);
            console.log(`📋 文件是否存在: ${fsSync.existsSync(requestedFile)}`);
            next();
        });
        
        app.use('/generated_images', express.static(imageSavePath));
    }
    
    // 视频路径处理（类似图片逻辑）
    if (isNetworkPath(videoSavePath)) {
        console.log(`🌐 检测到网络视频路径: ${videoSavePath}`);
        // 修改：不再返回不支持，而是像图片一样重定向到专门的媒体服务断点
        app.use('/generated_videos', (req, res, next) => {
            console.log(`[DIAGNOSTIC] /generated_videos 中间件触发，请求路径: ${req.path}`); // 日志1
            const requestedFile = path.join(videoSavePath, req.path);
            console.log(`🎬 网络视频资源请求: ${req.path} -> ${requestedFile}`);
            const filename = req.path.startsWith('/') ? req.path.substring(1) : req.path;
            const redirectUrl = `/api/media/videos/${encodeURIComponent(filename)}`;
            console.log(`🔄 [DIAGNOSTIC] 准备重定向到媒体端点: ${redirectUrl}`); // 日志2
            res.redirect(redirectUrl);
        });
    } else {
        console.log(`📁 使用本地视频路径: ${videoSavePath}`);
        
        app.use('/generated_videos', (req, res, next) => {
            const requestedFile = path.join(videoSavePath, req.path);
            console.log(`🎬 视频资源请求: ${req.path} -> ${requestedFile}`);
            console.log(`📋 文件是否存在: ${fsSync.existsSync(requestedFile)}`);
            next();
        });
        
        app.use('/generated_videos', express.static(videoSavePath));
    }
    
    console.log(`🌐 静态资源配置完成:`);
    console.log(`   图片路径: ${imageSavePath} ${isNetworkPath(imageSavePath) ? '(网络路径)' : '(本地路径)'}`);
    console.log(`   视频路径: ${videoSavePath} ${isNetworkPath(videoSavePath) ? '(网络路径)' : '(本地路径)'}`);
})();

// --- JavaScript Image Generation (Refactored for API Key) ---
async function generateImageInJS(prompt, negative = '模糊, 低质量', model = 'jimeng-3.0', strength = 0.8, width = 936, height = 1664) {
    const config = await loadAppConfig();
    const apiUrl = `${config.imageApiUrl}/v1/images/generations`;
    const imageSavePath = config.imageSavePath || path.join(__dirname, 'image', 'generated_images');
    const imageApiKey = config.imageApiKey;

    if (!imageApiKey) {
        const errorMessage = '请在设置中配置图片生成的 API Key';
        console.error(`❌ ${errorMessage}`);
        return { success: false, message: errorMessage };
    }

    // 确保输出目录存在
    if (!fsSync.existsSync(imageSavePath)) {
        await fs.mkdir(imageSavePath, { recursive: true });
    }
    
    console.log(`🚀 发送图片生成请求... 模型: ${model}, 尺寸: ${width}x${height}`);
    const maxRetries = 3;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'application/json',
                'Cookie': `session=${imageApiKey}` // 使用 Session Cookie 认证
            };

            const body = {
                prompt: prompt,
                n: 1,
                model: model,
                size: `${width}x${height}`,
                negative_prompt: negative,
                strength: strength,
                response_format: "b64_json"
            };
            
            const response = await axios.post(apiUrl, body, { headers, timeout: 600000 }); // 10分钟超时

            if (response.status === 200 && response.data && response.data.data && response.data.data[0].b64_json) {
                const imageData = response.data.data[0];
                const imageBuffer = Buffer.from(imageData.b64_json, 'base64');
                const timestamp = new Date().getTime();
                const safeFilename = generateSafeImageFilename(prompt, 0, timestamp) + '.png';
                const finalPath = path.join(imageSavePath, safeFilename);

                await fs.writeFile(finalPath, imageBuffer);

                console.log(`✅ 图片已保存至: ${finalPath}`);
                return {
                    success: true,
                    message: 'Image generated successfully.',
                    data: {
                        image_path: normalizePathForExpress(finalPath),
                        imageUrl: `/generated_images/${safeFilename}`
                    }
                };
            } else {
                 const errorText = response.data ? JSON.stringify(response.data) : 'Empty response';
                 console.error(`❌ API返回异常响应: ${response.status} - ${errorText}`);
                 throw new Error(`API returned an unusual response: ${response.status}`);
            }
        } catch (error) {
            let errorMessage = error.message;
            if (error.response) {
                errorMessage = `API返回异常: ${error.response.status} - ${JSON.stringify(error.response.data)}`;
            }
            console.error(`❌ 请求失败 (尝试 ${attempt}): ${errorMessage}`);
            if (attempt === maxRetries) {
                return { success: false, message: `图片生成失败，已达到最大重试次数: ${errorMessage}` };
            }
             await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒后重试
        }
    }
}

function generateSafeImageFilename(prompt, index, timestamp) {
    let safePrompt = 'image';
    if (prompt && String(prompt).trim()) {
        let decodedPrompt = String(prompt);
        try {
            if (decodedPrompt.includes('%')) {
                const unquoted = decodeURIComponent(decodedPrompt);
                if (unquoted !== decodedPrompt) {
                    decodedPrompt = unquoted;
                }
            }
        } catch (e) {
            console.warn(`⚠️ URL解码失败，使用原始prompt: ${e.message}`);
        }
        
        // 替换换行符和非法字符
        safePrompt = decodedPrompt
            .replace(/[\r\n]/g, ' ')
            .replace(/[<>:"/\\|?*%]/g, '_')
            .replace(/\s+/g, '_')
            .substring(0, 60)
            .replace(/[._ ]+$/, ''); // 移除末尾的点、下划线或空格
    }
    
    return `img_${timestamp}_${index + 1}_${safePrompt}.jpg`;
}

// --- JavaScript Video Generation ---
async function generateVideoInJS(prompt, taskId, outputDir, videoParams = {}) {
    return new Promise(async (resolve, reject) => {
        try {
            console.log(`⚡️ [JS] 开始生成视频，任务ID: ${taskId}, Prompt: ${prompt}`);
            console.log(`🎛️ [JS] 视频参数:`, videoParams);

            const config = await loadAppConfig();
            const videoApiUrl = config.videoApiUrl || 'http://localhost:8002';
            const apiUrl = `${videoApiUrl}/v1/chat/completions`;
            const videoApiKey = config.videoApiKey;

            // 构建请求头
            const headers = {
                'Content-Type': 'application/json'
            };

            if (config.videoRequireAuth && videoApiKey) {
                headers['Authorization'] = `Bearer ${videoApiKey}`;
            }

            // 构建API请求参数
            const payload = {
                model: "glm-video",
                messages: []
            };

            // 根据是否有图片构建不同的消息格式
            if (videoParams.image) {
                // 图生视频模式
                payload.messages.push({
                    role: "user",
                    content: [
                        { type: "text", text: prompt },
                        {
                            type: "image_url",
                            image_url: { url: videoParams.image }
                        }
                    ]
                });
            } else {
                // 文生视频模式
                payload.messages.push({
                    role: "user",
                    content: prompt
                });
            }

            // 添加视频生成参数
            if (videoParams.duration && videoParams.duration !== "5s") {
                payload.duration = videoParams.duration;
            }

            if (videoParams.aiSound) {
                payload.ai_sound = videoParams.aiSound;
            }

            // 仅在没有图片时添加aspect_ratio（文生视频模式）
            if (!videoParams.image && videoParams.aspectRatio) {
                payload.aspect_ratio = videoParams.aspectRatio;
            }

            console.log(`📤 [JS] 发送到ChatGLM视频API的参数:`, payload);
            const response = await axios.post(apiUrl, payload, {
                headers,
                timeout: 1800000 // 30分钟超时
            });

            // 处理ChatGLM API响应
            let videoUrl = null;

            // 尝试从不同的响应格式中提取视频链接
            if (response.data.choices && response.data.choices[0] && response.data.choices[0].message) {
                const content = response.data.choices[0].message.content;
                // 从Markdown链接中提取URL: [text](url)
                const match = content.match(/\((https:\/\/.*?)\)/);
                if (match) {
                    videoUrl = match[1];
                }
            } else if (response.data.video_url) {
                videoUrl = response.data.video_url;
            } else if (response.data.url) {
                videoUrl = response.data.url;
            }

            if (!videoUrl) {
                throw new Error('ChatGLM API 未返回有效的视频 URL');
            }

            console.log(`🔗 [JS] ChatGLM API 返回视频链接: ${videoUrl}`);

            // 委托给可重用的下载函数
            const downloadResult = await downloadVideo(videoUrl, prompt, outputDir);

            // 确保返回的数据结构与前端期望一致
            const result = {
                ...downloadResult,
                data: {
                    ...downloadResult,
                    videoUrl: downloadResult.video_url, // 添加前端期望的字段名
                    video_path: downloadResult.video_path // 保持原有字段
                }
            };

            resolve(result);

        } catch (error) {
            console.error(`❗️ [JS] 生成或下载视频时发生错误: ${error.message}`);
            reject({ success: false, message: error.message, error });
        }
    });
}

// 添加缺失的 calculateDimensions 函数
// 检测是否为网络路径
function isNetworkPath(filePath) {
    return typeof filePath === 'string' && (
        filePath.startsWith('\\\\') || // UNC路径 \\server\share
        filePath.startsWith('//') ||   // Unix网络路径
        /^[a-zA-Z]:\\/.test(filePath) === false && filePath.includes('\\\\') // 其他UNC格式
    );
}

function calculateDimensions(ratio) {
    const presetSizes = {
        '21:9': { width: 2016, height: 864 },
        '16:9': { width: 1664, height: 936 },
        '3:2': { width: 1584, height: 1056 },
        '4:3': { width: 1472, height: 1104 },
        '1:1': { width: 1328, height: 1328 },
        '3:4': { width: 1104, height: 1472 },
        '2:3': { width: 1056, height: 1584 },
        '9:16': { width: 936, height: 1664 }
    };
    
    if (ratio && presetSizes[ratio]) {
        return presetSizes[ratio];
    }
    
    // 默认返回 9:16 比例
    return { width: 936, height: 1664 };
}

// 调用 image.js 脚本生成图片
async function callImageScript(prompt, negative, model, strength, width, height, ws, rowId) {
    return new Promise(async (resolve, reject) => {
        const config = await loadAppConfig();
        const session = config.imageApiKey || '';
        
        // 生成一个唯一的基础文件名
        const timestamp = Date.now();
        const uniqueId = Math.random().toString(36).substring(2, 8);
        const baseFilename = `img_${timestamp}_${uniqueId}`;
        
        const args = [
            '--prompt', prompt,
            '--negative', negative,
            '--model', model,
            '--strength', strength,
            '--width', width,
            '--height', height,
            '--session', session,
            '--base-filename', baseFilename // 传递基础文件名
        ];

        console.log(`🚀 调用 image.js 脚本: node image.js ${args.join(' ')} `);
        
        const imageProcess = spawn('node', [path.join(__dirname, 'image.js'), ...args]);

        let output = '';
        const imageUrls = [];

        imageProcess.stdout.on('data', (data) => {
            const textData = data.toString();
            console.log(`[image.js] stdout: ${textData.trim()}`);
            output += textData;

            // 实时捕获图片URL（现在是本地URL）
            const urlRegex = /IMAGE_URL_START:(.*?):IMAGE_URL_END/g;
            let match;
            while ((match = urlRegex.exec(textData)) !== null) {
                const localUrl = match[1];
                imageUrls.push(localUrl);
                console.log(`[Server] 实时捕获到本地图片 URL: ${localUrl}`);
            }

            // 实时捕获URL映射信息
            const mappingRegex = /URL_MAPPING_START:(.*?):(.*?):URL_MAPPING_END/g;
            let mappingMatch;
            while ((mappingMatch = mappingRegex.exec(textData)) !== null) {
                const localUrl = mappingMatch[1];
                const remoteUrl = mappingMatch[2];
                imageUrlMapping.set(localUrl, remoteUrl);
                console.log(`[Server] 建立URL映射: ${localUrl} -> ${remoteUrl}`);
            }
        });

        imageProcess.stderr.on('data', (data) => {
            console.error(`[image.js] stderr: ${data}`);
            output += data.toString();
        });
        
        imageProcess.on('error', (err) => {
            console.error('无法启动 image.js 脚本:', err);
            ws.send(JSON.stringify({ 
                type: 'image-status-update', 
                data: { rowId: rowId, status: 'error', message: '无法启动图片生成脚本' } 
            }));
            reject(err); // Promise 失败
        });

        imageProcess.on('close', (code) => {
            if (code !== 0) {
                console.error(`image.js 脚本以代码 ${code} 退出`);
                ws.send(JSON.stringify({ 
                    type: 'image-status-update', 
                    data: { rowId: rowId, status: 'error', message: `图片生成失败 (code: ${code})` } 
                }));
                reject(new Error(`image.js 脚本以代码 ${code} 退出`)); // Promise 失败
            } else {
                console.log(`✅ image.js 脚本执行完毕`);
                // 所有图片都处理完后，发送最终状态
                ws.send(JSON.stringify({
                    type: 'image-status-update',
                    data: { rowId: rowId, status: 'completed', images: imageUrls }
                }));
                resolve(); // Promise 成功
            }
        });
    });
}

// 调用 video.js 脚本生成视频 - 使用环境变量传递图片路径
async function callVideoScript(imagePath, prompt) {
    return new Promise((resolve, reject) => {
        const args = [
            '--mode', 'local',
            '--prompt', prompt || '创建一个有趣的视频'
        ];

        console.log('🚀 调用 video.js 脚本:', `node video.js ${args.join(' ')}`);
        console.log(`📷 图片路径: ${imagePath}`);
        
        // 临时复制图片到 video.js 期望的位置
        const tempImagePath = path.join(__dirname, '1.jpg');
        try {
            fsSync.copyFileSync(imagePath, tempImagePath);
            console.log(`📋 临时复制图片到: ${tempImagePath}`);
        } catch (copyError) {
            reject(new Error(`无法复制图片文件: ${copyError.message}`));
            return;
        }
        
        const videoProcess = spawn('node', ['video.js', ...args], {
            cwd: __dirname,
            stdio: ['pipe', 'pipe', 'pipe']
        });

        let stdout = '';
        let stderr = '';

        videoProcess.stdout.on('data', (data) => {
            stdout += data.toString();
            console.log(`[video.js] ${data.toString().trim()}`);
        });

        videoProcess.stderr.on('data', (data) => {
            stderr += data.toString();
            console.error(`[video.js] ERROR: ${data.toString().trim()}`);
        });

        videoProcess.on('close', (code) => {
            // 清理临时文件
            try {
                if (fsSync.existsSync(tempImagePath)) {
                    fsSync.unlinkSync(tempImagePath);
                    console.log(`🗑️ 清理临时文件: ${tempImagePath}`);
                }
            } catch (cleanupError) {
                console.warn(`⚠️ 清理临时文件失败: ${cleanupError.message}`);
            }

            if (code === 0) {
                // 从输出中提取视频链接
                const videoUrlMatch = stdout.match(/🎬 视频链接: (https:\/\/[^\s]+)/);
                if (videoUrlMatch) {
                    const videoUrl = videoUrlMatch[1];
                    resolve({
                        success: true,
                        message: '视频生成成功',
                        data: {
                            video_url: videoUrl,
                            prompt: prompt,
                            image_path: imagePath
                        }
                    });
                } else {
                    resolve({
                        success: true,
                        message: '视频脚本执行完成',
                        data: { stdout, stderr }
                    });
                }
            } else {
                reject(new Error(`video.js 脚本执行失败，退出码: ${code}\nstderr: ${stderr}\nstdout: ${stdout}`));
            }
        });

        videoProcess.on('error', (error) => {
            reject(new Error(`无法启动 video.js 脚本: ${error.message}`));
        });
    });
}

// --- API Endpoints ---
app.get('/api/health', (req, res) => {
    res.json({ success: true, message: '服务运行正常', timestamp: new Date().toISOString() });
});

// 新增：通用的媒体文件服务断点，支持图片和视频
app.get('/api/media/:type/:filename', async (req, res) => {
    console.log(`[DIAGNOSTIC] /api/media 端点被调用`); // 日志3
    try {
        const { type, filename: encodedFilename } = req.params;
        const filename = decodeURIComponent(encodedFilename);
        const config = await loadAppConfig();

        let basePath;
        let contentType;

        if (type === 'images') {
            basePath = config.imageSavePath;
            // 简单的内容类型推断
            if (filename.endsWith('.png')) contentType = 'image/png';
            else if (filename.endsWith('.webp')) contentType = 'image/webp';
            else contentType = 'image/jpeg';
        } else if (type === 'videos') {
            basePath = config.videoSavePath;
            contentType = 'video/mp4';
        } else {
            return res.status(400).json({ success: false, message: '无效的媒体类型' });
        }

        if (!basePath || !isNetworkPath(basePath)) {
             // 如果不是网络路径，理论上应该由 express.static 处理，这里作为后备
            return res.status(404).json({ success: false, message: '请求的资源路径未配置为网络路径' });
        }
        
        const fullPath = path.join(basePath, filename);
        console.log(`[DIAGNOSTIC] 计算出的完整文件路径: ${fullPath}`); // 日志4
        console.log(`📦 [Media Endpoint] 媒体文件请求: type=${type}, filename=${filename}`);
        console.log(`  -> 完整路径: ${fullPath}`);

        const fileExists = fsSync.existsSync(fullPath);
        console.log(`[DIAGNOSTIC] 文件是否存在检查: ${fileExists}`); // 日志5

        if (fileExists) {
            console.log(`[DIAGNOSTIC] 文件存在，准备发送文件...`); // 日志6
            const fileStats = fsSync.statSync(fullPath);
            const fileStream = fsSync.createReadStream(fullPath);

            res.setHeader('Content-Type', contentType);
            res.setHeader('Content-Length', fileStats.size);
            res.setHeader('Cache-Control', 'public, max-age=31536000');
            res.setHeader('Last-Modified', fileStats.mtime.toUTCString());
            // 支持范围请求，这对视频流至关重要
            res.setHeader('Accept-Ranges', 'bytes');

            console.log(`✅ [Media Endpoint] 提供媒体文件: ${fullPath}`);
            fileStream.pipe(res);
        } else {
            console.log(`❌ [DIAGNOSTIC] 文件未找到，路径: ${fullPath}`); // 日志7
            console.log(`❌ [Media Endpoint] 媒体文件不存在: ${fullPath}`);
            res.status(404).json({ success: false, message: '文件不存在' });
        }

    } catch (error) {
        console.error('媒体文件访问失败:', error);
        res.status(500).json({ success: false, message: error.message });
    }
});

// 网络路径文件读取端点
app.get('/api/network-file/:filename', async (req, res) => {
    try {
        const filename = decodeURIComponent(req.params.filename);
        const config = await loadAppConfig();
        const imageSavePath = config.imageSavePath || path.join(__dirname, 'image', 'generated_images');
        
        // 检查是否为网络路径
        if (isNetworkPath(imageSavePath)) {
            const fullPath = path.join(imageSavePath, filename);
            console.log(`🌐 网络文件请求: ${filename}`);
            console.log(`📁 完整路径: ${fullPath}`);
            
            if (fsSync.existsSync(fullPath)) {
                const fileStats = fsSync.statSync(fullPath);
                const fileStream = fsSync.createReadStream(fullPath);
                
                // 设置适当的响应头
                res.setHeader('Content-Type', 'image/jpeg');
                res.setHeader('Content-Length', fileStats.size);
                res.setHeader('Cache-Control', 'public, max-age=31536000'); // 缓存一年
                res.setHeader('Last-Modified', fileStats.mtime.toUTCString());
                
                console.log(`✅ 提供网络文件: ${fullPath}`);
                fileStream.pipe(res);
            } else {
                console.log(`❌ 网络文件不存在: ${fullPath}`);
                res.status(404).json({ success: false, message: '文件不存在' });
            }
        } else {
            // 非网络路径，重定向到标准静态资源
            res.redirect(`/generated_images/${encodeURIComponent(filename)}`);
        }
    } catch (error) {
        console.error('网络文件访问失败:', error);
        res.status(500).json({ success: false, message: error.message });
    }
});

// 测试静态资源访问的接口
app.get('/api/test-static/:filename', async (req, res) => {
    try {
        const filename = req.params.filename;
        const config = await loadAppConfig();
        const imageSavePath = config.imageSavePath || path.join(__dirname, 'image', 'generated_images');
        const fullPath = path.join(imageSavePath, filename);
        
        console.log(`🔍 测试文件访问: ${filename}`);
        console.log(`📁 完整路径: ${fullPath}`);
        console.log(`📋 文件存在: ${fsSync.existsSync(fullPath)}`);
        console.log(`🌐 是否为网络路径: ${isNetworkPath(imageSavePath)}`);
        
        if (fsSync.existsSync(fullPath)) {
            const stats = fsSync.statSync(fullPath);
            const accessUrl = isNetworkPath(imageSavePath) 
                ? `/api/network-file/${encodeURIComponent(filename)}`
                : `/generated_images/${encodeURIComponent(filename)}`;
                
            res.json({
                success: true,
                filename: filename,
                fullPath: fullPath,
                exists: true,
                size: stats.size,
                mtime: stats.mtime,
                isNetworkPath: isNetworkPath(imageSavePath),
                accessUrl: accessUrl,
                directUrl: `http://localhost:3001${accessUrl}`
            });
        } else {
            res.json({
                success: false,
                filename: filename,
                fullPath: fullPath,
                exists: false,
                isNetworkPath: isNetworkPath(imageSavePath),
                message: '文件不存在'
            });
        }
    } catch (error) {
        console.error('测试静态资源访问失败:', error);
        res.status(500).json({ success: false, message: error.message });
    }
});

app.post('/api/config', async (req, res) => {
    // 使用新的配置结构，支持独立的视频API配置
    const { imageApiUrl, imageApiKey, videoApiUrl, videoRequireAuth, videoApiKey, videoSavePath, imageSavePath, imageSessions } = req.body;
    try {
        // 兼容性处理：如果前端还在发送imageSessions，则映射到imageApiKey
        const configToSave = {
            imageApiUrl,
            imageApiKey: imageApiKey || imageSessions, // 优先使用imageApiKey，否则使用imageSessions
            videoApiUrl,
            videoRequireAuth: videoRequireAuth || false,
            videoApiKey: videoApiKey || '',
            videoSavePath,
            imageSavePath
        };
        
        await saveAppConfig(configToSave);
        // After saving, we need to update the static serving paths if they changed
        // 注：静态路径的更改需要重启服务器才能生效
        res.json({ success: true, message: '配置已保存' });
    } catch (error) {
        console.error('保存配置错误:', error);
        res.status(500).json({ success: false, message: '保存配置失败' });
    }
});

app.get('/api/config', async (req, res) => {
    try {
        const config = await loadAppConfig();
        res.json(config);
    } catch (error) {
        res.status(500).json({ success: false, message: '加载配置失败' });
    }
});

app.post('/api/generate-video', async (req, res) => {
    console.log('接收到视频生成请求:', req.body);
    const { image, prompt, rowId, duration, aiSound, aspectRatio } = req.body;
    if (!image) return res.status(400).json({ success: false, message: '必须提供图片数据' });

    let tempImagePath = null;

    try {
        let actualImageToUse = image;
        
        // 使用统一的图片URL处理函数
        if (typeof image === 'string' && image.startsWith('/generated_images/')) {
            console.log(`🔍 处理图片路径: ${image}`);
            actualImageToUse = getActualImageUrl(image);
            console.log(`🔍 getActualImageUrl 返回: ${actualImageToUse}`);
            if (!actualImageToUse) {
                console.warn(`❌ 无法获取图片的有效URL: ${image}`);
                return res.status(400).json({
                    success: false,
                    message: '无法获取图片的有效URL，请检查图片文件是否存在'
                });
            }
        }

        // 如果是URL格式，直接使用generateVideoInJS
        if (typeof actualImageToUse === 'string' && actualImageToUse.startsWith('http')) {
            console.log(`🌐 使用图片URL生成视频: ${actualImageToUse}`);

            // 获取视频保存路径
            const config = await loadAppConfig();
            const outputDir = config.videoSavePath || path.join(__dirname, 'videos');

            const result = await generateVideoInJS(prompt, rowId, outputDir, {
                image: actualImageToUse,
                duration,
                aiSound,
                aspectRatio
            });

            // 如果提供了rowId，通过WebSocket通知前端状态更新
            if (rowId && result) {
                const wsMessage = {
                    type: 'video_update',
                    data: {
                        originalRowId: rowId,
                        success: result.success,
                        videoPath: result.success ? result.data?.videoUrl : null, // 使用相对URL而不是绝对路径
                        error: result.success ? null : (result.message || '视频生成失败')
                    }
                };
                console.log(`📤 发送视频生成完成通知到前端，行ID: ${rowId}`);
                broadcastToWebSocketClients(wsMessage);
            }

            return res.json(result);
        }

        // 以下是原有的Data URL处理逻辑（作为后备）
        // 创建临时目录
        const tempDir = path.join(__dirname, 'temp');
        if (!fsSync.existsSync(tempDir)) {
            await fs.mkdir(tempDir, { recursive: true });
        }

        // 解码Data URL
        const base64Match = actualImageToUse.match(/^data:image\/(jpeg|jpg|png|webp);base64,(.+)$/);
        if (!base64Match) {
            return res.status(400).json({ success: false, message: '无效的图片数据格式，需要Data URL格式或本地URL' });
        }

        const imageBuffer = Buffer.from(base64Match[2], 'base64');
        const imageFormat = base64Match[1];
        
        // 创建唯一的临时文件名
        const timestamp = Date.now();
        const randomSuffix = Math.random().toString(36).substring(2, 8);
        const tempFileName = `ref_${timestamp}_${randomSuffix}.${imageFormat === 'jpeg' ? 'jpg' : imageFormat}`;
        tempImagePath = path.join(tempDir, tempFileName);

        // 将图片数据写入临时文件
        await fs.writeFile(tempImagePath, imageBuffer);
        console.log(`📁 创建临时参考图片: ${tempImagePath}`);

        // 调用 video.js 脚本
        const result = await callVideoScript(tempImagePath, prompt);

        if (result.success && result.data.video_url) {
            // 如果有实际的视频文件下载，需要处理文件路径
            const responseData = {
                ...result,
                data: {
                    ...result.data,
                    videoUrl: result.data.video_url // 保持与前端兼容的字段名
                }
            };
            
            // 如果提供了rowId，通过WebSocket通知前端状态更新
            if (rowId && result) {
                const wsMessage = {
                    type: 'video_update',
                    data: {
                        originalRowId: rowId,
                        success: result.success,
                        videoPath: result.success ? result.data?.video_path : null,
                        error: result.success ? null : (result.message || '视频生成失败')
                    }
                };
                console.log(`📤 发送视频生成完成通知到前端（后备路径），行ID: ${rowId}`);
                broadcastToWebSocketClients(wsMessage);
            }
            
            res.json(responseData);
        } else {
            // 如果提供了rowId，通知前端失败状态
            if (rowId) {
                const wsMessage = {
                    type: 'video_update',
                    data: {
                        originalRowId: rowId,
                        success: false,
                        videoPath: null,
                        error: result.message || '视频生成失败'
                    }
                };
                console.log(`📤 发送视频生成失败通知到前端，行ID: ${rowId}`);
                broadcastToWebSocketClients(wsMessage);
            }
            
            res.json(result);
        }
    } catch (error) {
        console.error('视频生成失败:', error);
        
        // 如果提供了rowId，通知前端错误状态
        if (rowId) {
            const wsMessage = {
                type: 'video_update',
                data: {
                    originalRowId: rowId,
                    success: false,
                    videoPath: null,
                    error: `视频生成执行失败: ${error.message}`
                }
            };
            console.log(`📤 发送视频生成错误通知到前端，行ID: ${rowId}`);
            broadcastToWebSocketClients(wsMessage);
        }
        
        res.status(500).json({ success: false, message: `视频生成执行失败: ${error.message}` });
    } finally {
        // 清理临时文件
        if (tempImagePath && fsSync.existsSync(tempImagePath)) {
            try {
                await fs.unlink(tempImagePath);
                console.log(`🗑️ 已清理临时文件: ${tempImagePath}`);
            } catch (cleanupError) {
                console.warn(`⚠️ 清理临时文件失败: ${tempImagePath}`, cleanupError);
            }
        }
    }
});

// --- Task Management Endpoints ---
const TASKS_DIR = path.join(__dirname, 'tasks');
app.post('/api/tasks/:taskName', async (req, res) => {
    try {
        const taskName = req.params.taskName.replace(/[^a-zA-Z0-9_\-\u4e00-\u9fff]/g, '');
        if (!taskName) return res.status(400).json({ success: false, message: "任务名称无效" });
        const filePath = path.join(TASKS_DIR, `${taskName}.json`);
        await fs.writeFile(filePath, JSON.stringify(req.body, null, 2), 'utf-8');
        res.json({ success: true, message: `任务 '${taskName}' 已保存` });
    } catch (error) {
        console.error('保存任务失败:', error);
        res.status(500).json({ success: false, message: '保存任务失败' });
    }
});

app.get('/api/tasks', async (req, res) => {
    try {
        if (!fsSync.existsSync(TASKS_DIR)) await fs.mkdir(TASKS_DIR, { recursive: true });
        const files = await fs.readdir(TASKS_DIR);
        const taskNames = files.filter(file => file.endsWith('.json')).map(file => file.replace('.json', ''));
        res.json(taskNames);
    } catch (error) {
        console.error('获取任务列表失败:', error);
        res.status(500).json({ success: false, message: '获取任务列表失败' });
    }
});

app.get('/api/tasks/:taskName', async (req, res) => {
    try {
        const taskName = req.params.taskName.replace(/[^a-zA-Z0-9_\-\u4e00-\u9fff]/g, '');
        const filePath = path.join(TASKS_DIR, `${taskName}.json`);
        if (fsSync.existsSync(filePath)) {
            const data = await fs.readFile(filePath, 'utf-8');
            const taskData = JSON.parse(data);

            // 处理任务数据，将远程URL转换为本地URL（如果本地文件存在）
            if (taskData.rows && Array.isArray(taskData.rows)) {
                taskData.rows = taskData.rows.map(row => {
                    return convertRemoteUrlsToLocal(row);
                });
            }

            res.json(taskData);
        } else {
            res.status(404).json({ success: false, message: '任务不存在' });
        }
    } catch (error) {
        console.error('加载任务失败:', error);
        res.status(500).json({ success: false, message: '加载任务失败' });
    }
});

app.delete('/api/tasks/:taskName', async (req, res) => {
    try {
        const taskName = req.params.taskName.replace(/[^a-zA-Z0-9_\-\u4e00-\u9fff]/g, '');
        const filePath = path.join(TASKS_DIR, `${taskName}.json`);
        if (fsSync.existsSync(filePath)) {
            await fs.unlink(filePath);
            res.json({ success: true, message: `任务 '${taskName}' 已删除` });
        } else {
            res.status(404).json({ success: false, message: '任务不存在' });
        }
    } catch (error) {
        console.error('删除任务失败:', error);
        res.status(500).json({ success: false, message: '删除任务失败' });
    }
});

// --- Other Endpoints ---
app.post('/api/upload-image', async (req, res) => {
    try {
        const { imageData, fileName } = req.body;
        if (!imageData || !fileName) return res.status(400).json({ success: false, message: '缺少图片数据或文件名' });
        const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp'];
        const fileExt = path.extname(fileName).toLowerCase();
        if (!allowedExtensions.includes(fileExt)) return res.status(400).json({ success: false, message: '不支持的文件格式' });
        const base64Match = imageData.match(/^data:image\/(jpeg|jpg|png|webp);base64,(.+)$/);
        if (!base64Match) return res.status(400).json({ success: false, message: '无效的图片数据格式' });
        const imageBuffer = Buffer.from(base64Match[2], 'base64');
        if (imageBuffer.length > 5 * 1024 * 1024) return res.status(400).json({ success: false, message: '图片文件过大' });
        const timestamp = Date.now();
        const randomSuffix = Math.random().toString(36).substring(2, 8);
        let baseName = path.parse(fileName).name.replace(/[<>:"/\\|?*%]/g, '_').replace(/\s+/g, '_').substring(0, 30);
        const uniqueFileName = `upload_${timestamp}_${randomSuffix}_${baseName}${fileExt}`;
        const config = await loadAppConfig();
        let uploadPath = config.imageSavePath ? config.imageSavePath.trim() : path.join(__dirname, 'image', 'generated_images');
        if (!fsSync.existsSync(uploadPath)) await fs.mkdir(uploadPath, { recursive: true });
        const imagePath = path.join(uploadPath, uniqueFileName);
        await fs.writeFile(imagePath, imageBuffer);
        res.json({ success: true, imageUrl: `/generated_images/${uniqueFileName}`, fileName: uniqueFileName, message: '图片上传成功' });
    } catch (error) {
        console.error('图片上传失败:', error);
        res.status(500).json({ success: false, message: error.message || '图片上传失败' });
    }
});

app.post('/api/open-folder', async (req, res) => {
    try {
        const { type, path: customPath } = req.body;
        let targetPath;
        if (customPath) {
            targetPath = customPath;
        } else {
            const config = await loadAppConfig();
            targetPath = type === 'video' 
                ? (config.videoSavePath || path.join(__dirname, 'image', 'generated_videos'))
                : (config.imageSavePath || path.join(__dirname, 'image', 'generated_images'));
        }
        if (!fsSync.existsSync(targetPath)) return res.status(404).json({ success: false, message: `文件夹不存在: ${targetPath}` });
        const platform = process.platform;
        let command, args;
        switch (platform) {
            case 'win32': command = 'explorer'; args = [targetPath.replace(/\//g, '\\')]; break;
            case 'darwin': command = 'open'; args = [targetPath]; break;
            case 'linux': command = 'xdg-open'; args = [targetPath]; break;
            default: return res.status(500).json({ success: false, message: `不支持的操作系统: ${platform}` });
        }
        const openProcess = spawn(command, args, { detached: true, stdio: 'ignore' });
        openProcess.unref();
        res.json({ success: true, message: `已尝试打开文件夹: ${targetPath}`, path: targetPath });
    } catch (error) {
        console.error('打开文件夹错误:', error);
        res.status(500).json({ success: false, message: error.message || '打开文件夹失败' });
    }
});

// 添加服务器信息端点
app.get('/api/server-info', (req, res) => {
    const serverUrl = getServerUrl();
    res.json({
        serverUrl: serverUrl,
        port: PORT,
        localUrl: `http://localhost:${PORT}`,
        message: '服务器信息'
    });
});

// --- Server Start ---
server.listen(PORT, '0.0.0.0', () => {
    const serverUrl = getServerUrl();
    console.log(`🚀 后端服务器启动成功，监听端口 http://localhost:${PORT}`);
    console.log(`🌐 局域网访问地址: ${serverUrl}`);
    console.log(`📱 移动设备可通过以下地址访问: ${serverUrl}`);
});